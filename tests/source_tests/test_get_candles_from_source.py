"""
Test suite for get_candles_from_{source} functions.

This test file discovers and runs tests for all get_candles_from_{source}.py modules
in the app/sources/{source}/ directory automatically.
"""

import os
import sys
import importlib
import inspect
import pytest
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import List, Tuple, Callable

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def discover_source_functions() -> List[Tuple[str, Callable]]:
    """
    Discover all get_candles_from_{source} functions in app/sources/{source}/ directories.

    Returns:
        List of tuples containing (source_name, function_callable)
    """
    sources_path = os.path.join(project_root, 'app', 'sources')
    discovered_functions = []

    if not os.path.exists(sources_path):
        pytest.skip(f"Sources directory not found: {sources_path}")
        return discovered_functions

    # Iterate through each source directory
    for source_dir in os.listdir(sources_path):
        source_path = os.path.join(sources_path, source_dir)

        # Skip if not a directory or if it's __pycache__
        if not os.path.isdir(source_path) or source_dir.startswith('__'):
            continue

        # Look for get_candles_from_{source}.py file
        expected_filename = f"get_candles_from_{source_dir}.py"
        expected_filepath = os.path.join(source_path, expected_filename)

        if os.path.exists(expected_filepath):
            try:
                # Import the module
                module_name = f"app.sources.{source_dir}.get_candles_from_{source_dir}"
                module = importlib.import_module(module_name)

                # Get the function
                function_name = f"get_candles_from_{source_dir}"
                if hasattr(module, function_name):
                    function = getattr(module, function_name)
                    discovered_functions.append((source_dir, function))
                    print(f"✓ Discovered: {function_name} from {module_name}")
                else:
                    print(f"✗ Function {function_name} not found in {module_name}")

            except ImportError as e:
                print(f"✗ Failed to import {module_name}: {e}")
            except Exception as e:
                print(f"✗ Error discovering {source_dir}: {e}")

    return discovered_functions


def validate_dataframe_response(df: pd.DataFrame, source_name: str, expected_candles: int = 5) -> Tuple[bool, List[str]]:
    """
    Validate that the DataFrame response meets the requirements.

    Args:
        df: DataFrame to validate
        source_name: Name of the source for error reporting
        expected_candles: Expected number of candles

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    # Check if DataFrame is None or empty
    if df is None:
        errors.append(f"{source_name}: Function returned None")
        return False, errors

    if df.empty:
        errors.append(f"{source_name}: Function returned empty DataFrame")
        return False, errors

    # Check if DataFrame has the expected number of rows (at least expected_candles)
    if len(df) < expected_candles:
        errors.append(f"{source_name}: Expected at least {expected_candles} rows, got {len(df)}")

    # Check if DataFrame has timestamp column
    if 'timestamp' not in df.columns:
        errors.append(f"{source_name}: DataFrame missing 'timestamp' column. Available columns: {list(df.columns)}")
        return False, errors

    # Check if timestamps are valid
    try:
        # Convert timestamps to datetime if they're not already
        if df['timestamp'].dtype == 'object':
            # Try to convert string timestamps to numeric
            timestamps = pd.to_numeric(df['timestamp'], errors='coerce')
        else:
            timestamps = df['timestamp']

        # Check for any NaN timestamps
        if timestamps.isna().any():
            errors.append(f"{source_name}: DataFrame contains invalid timestamps")
            return False, errors

        # Get the newest timestamp
        newest_timestamp = timestamps.max()
        current_time = datetime.now().timestamp()

        # Check if newest timestamp is not older than 2 minutes ago
        time_diff_minutes = (current_time - newest_timestamp) / 60
        if time_diff_minutes > 2:
            errors.append(f"{source_name}: Newest timestamp is {time_diff_minutes:.1f} minutes old (should be ≤ 2 minutes)")

        # Check if we have at least the expected number of different timestamps
        unique_timestamps = timestamps.nunique()
        if unique_timestamps < expected_candles:
            errors.append(f"{source_name}: Expected at least {expected_candles} unique timestamps, got {unique_timestamps}")

    except Exception as e:
        errors.append(f"{source_name}: Error validating timestamps: {str(e)}")
        return False, errors

    # Check for required OHLC columns
    required_columns = ['open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"{source_name}: Missing required columns: {missing_columns}")

    return len(errors) == 0, errors


class TestGetCandlesFromSource:
    """Test class for all get_candles_from_{source} functions."""

    @pytest.fixture(scope="class")
    def discovered_sources(self):
        """Fixture to discover all source functions once per test class."""
        return discover_source_functions()

    def test_discovery_finds_sources(self, discovered_sources):
        """Test that we can discover at least one source function."""
        assert len(discovered_sources) > 0, "No source functions were discovered"
        print(f"\nDiscovered {len(discovered_sources)} source functions:")
        for source_name, _ in discovered_sources:
            print(f"  - {source_name}")

    @pytest.mark.parametrize("source_name,source_function", discover_source_functions())
    def test_get_candles_basic_functionality(self, source_name, source_function):
        """
        Test 1: Run get_candles_from_{source} with standard parameters.

        Parameters:
        - currency_pair = 'btc-usd'
        - timeframe = 'm1'
        - candles = 5
        - ecc = True

        Expected:
        - Returns a DataFrame with 5 rows
        - Newest timestamp not older than 2 minutes ago
        - Contains at least 5 older timestamps (can contain more)
        """
        print(f"\n🧪 Testing {source_name} with basic parameters...")

        # Test parameters as specified in the comment
        currency_pair = 'btc-usd'
        timeframe = 'm1'
        candles = 5
        ecc = True

        try:
            # Call the source function
            result = source_function(currency_pair, timeframe, candles, ecc)

            # Validate the response
            is_valid, errors = validate_dataframe_response(result, source_name, candles)

            if not is_valid:
                error_msg = f"Validation failed for {source_name}:\n" + "\n".join(f"  - {error}" for error in errors)
                pytest.fail(error_msg)

            # Additional success logging
            print(f"✓ {source_name}: Successfully returned {len(result)} candles")
            if not result.empty:
                newest_ts = result['timestamp'].max()
                oldest_ts = result['timestamp'].min()
                current_time = datetime.now().timestamp()
                age_minutes = (current_time - newest_ts) / 60
                print(f"  - Data age: {age_minutes:.1f} minutes")
                print(f"  - Time range: {(newest_ts - oldest_ts) / 60:.1f} minutes")

        except Exception as e:
            pytest.fail(f"Exception in {source_name}: {str(e)}")


if __name__ == "__main__":
    # Allow running the test file directly for debugging
    print("Discovering source functions...")
    sources = discover_source_functions()

    if not sources:
        print("❌ No source functions discovered")
        sys.exit(1)

    print(f"\n✓ Found {len(sources)} source functions")

    # Run a quick test on each discovered function
    for source_name, source_function in sources:
        print(f"\n🧪 Quick test of {source_name}...")
        try:
            result = source_function('btc-usd', 'm1', 5, True)
            is_valid, errors = validate_dataframe_response(result, source_name, 5)

            if is_valid:
                print(f"✓ {source_name}: PASSED")
            else:
                print(f"❌ {source_name}: FAILED")
                for error in errors:
                    print(f"    {error}")

        except Exception as e:
            print(f"❌ {source_name}: EXCEPTION - {str(e)}")

    print("\n🏁 Discovery and quick testing complete!")