"""
Test suite for get_candles_from_{source} functions.

This test file discovers and runs tests for all get_candles_from_{source}.py modules
in the app/sources/{source}/ directory automatically.
"""

import os
import sys
import importlib
import inspect
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Tuple, Callable

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def discover_source_functions() -> List[Tuple[str, Callable]]:
    """
    Discover all get_candles_from_{source} functions in app/sources/{source}/ directories.

    Returns:
        List of tuples containing (source_name, function_callable)
    """
    sources_path = os.path.join(project_root, 'app', 'sources')
    discovered_functions = []

    if not os.path.exists(sources_path):
        print(f"⚠️  Sources directory not found: {sources_path}")
        return discovered_functions

    # Iterate through each source directory
    for source_dir in os.listdir(sources_path):
        source_path = os.path.join(sources_path, source_dir)

        # Skip if not a directory or if it's __pycache__
        if not os.path.isdir(source_path) or source_dir.startswith('__'):
            continue

        # Look for get_candles_from_{source}.py file
        expected_filename = f"get_candles_from_{source_dir}.py"
        expected_filepath = os.path.join(source_path, expected_filename)

        if os.path.exists(expected_filepath):
            try:
                # Import the module
                module_name = f"app.sources.{source_dir}.get_candles_from_{source_dir}"
                module = importlib.import_module(module_name)

                # Get the function
                function_name = f"get_candles_from_{source_dir}"
                if hasattr(module, function_name):
                    function = getattr(module, function_name)
                    discovered_functions.append((source_dir, function))
                    print(f"✓ Discovered: {function_name} from {module_name}")
                else:
                    print(f"✗ Function {function_name} not found in {module_name}")

            except ImportError as e:
                print(f"✗ Failed to import {module_name}: {e}")
            except Exception as e:
                print(f"✗ Error discovering {source_dir}: {e}")

    return discovered_functions


def get_dataframe_sample(df: pd.DataFrame, n: int = 3) -> dict:
    """
    Get sample data from DataFrame (head and tail).

    Args:
        df: DataFrame to sample
        n: Number of rows for head and tail

    Returns:
        Dictionary with head and tail samples
    """
    if df is None or df.empty:
        return {"head": "No data", "tail": "No data"}

    try:
        # Get head and tail samples
        head_sample = df.head(n)
        tail_sample = df.tail(n)

        # Convert to string representation with better formatting
        head_str = head_sample.to_string(index=False, max_cols=10, float_format=lambda x: f'{x:.4f}' if pd.notnull(x) else 'NaN')
        tail_str = tail_sample.to_string(index=False, max_cols=10, float_format=lambda x: f'{x:.4f}' if pd.notnull(x) else 'NaN')

        return {
            "head": head_str,
            "tail": tail_str,
            "total_rows": len(df),
            "columns": list(df.columns)
        }
    except Exception as e:
        return {"head": f"Error sampling data: {e}", "tail": "", "total_rows": len(df) if df is not None else 0}


def validate_dataframe_response(df: pd.DataFrame, source_name: str, expected_candles: int = 5) -> Tuple[bool, List[str], dict]:
    """
    Validate that the DataFrame response meets the requirements.

    Args:
        df: DataFrame to validate
        source_name: Name of the source for error reporting
        expected_candles: Expected number of candles

    Returns:
        Tuple of (is_valid, list_of_errors, sample_data)
    """
    errors = []
    sample_data = get_dataframe_sample(df)

    # Check if DataFrame is None or empty
    if df is None:
        errors.append(f"{source_name}: Function returned None")
        return False, errors, sample_data

    if df.empty:
        errors.append(f"{source_name}: Function returned empty DataFrame")
        return False, errors, sample_data

    # Check if DataFrame has the expected number of rows (at least expected_candles)
    if len(df) < expected_candles:
        errors.append(f"{source_name}: Expected at least {expected_candles} rows, got {len(df)}")

    # Check if DataFrame has timestamp column
    if 'timestamp' not in df.columns:
        errors.append(f"{source_name}: DataFrame missing 'timestamp' column. Available columns: {list(df.columns)}")
        return False, errors, sample_data

    # Check if timestamps are valid
    try:
        # Convert timestamps to datetime if they're not already
        if df['timestamp'].dtype == 'object':
            # Try to convert string timestamps to numeric
            timestamps = pd.to_numeric(df['timestamp'], errors='coerce')
        else:
            timestamps = df['timestamp']

        # Check for any NaN timestamps
        if timestamps.isna().any():
            errors.append(f"{source_name}: DataFrame contains invalid timestamps")
            return False, errors, sample_data

        # Get the newest timestamp
        newest_timestamp = timestamps.max()
        current_time = datetime.now().timestamp()

        # Check if newest timestamp is not older than 2 minutes ago
        time_diff_minutes = (current_time - newest_timestamp) / 60
        if time_diff_minutes > 2:
            errors.append(f"{source_name}: Newest timestamp is {time_diff_minutes:.1f} minutes old (should be ≤ 2 minutes)")

        # Check if we have at least the expected number of different timestamps
        unique_timestamps = timestamps.nunique()
        if unique_timestamps < expected_candles:
            errors.append(f"{source_name}: Expected at least {expected_candles} unique timestamps, got {unique_timestamps}")

    except Exception as e:
        errors.append(f"{source_name}: Error validating timestamps: {str(e)}")
        return False, errors, sample_data

    # Check for required OHLC columns
    required_columns = ['open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"{source_name}: Missing required columns: {missing_columns}")

    return len(errors) == 0, errors, sample_data


class TestResult:
    """Simple test result container."""
    def __init__(self, test_name: str, source_name: str, passed: bool, message: str = "", details: dict = None, sample_data: dict = None):
        self.test_name = test_name
        self.source_name = source_name
        self.passed = passed
        self.message = message
        self.details = details or {}
        self.sample_data = sample_data or {}


class SourceTestRunner:
    """Simple test runner for get_candles_from_{source} functions."""

    def __init__(self):
        self.results = []
        self.discovered_sources = []

    def discover_sources(self) -> bool:
        """Discover all source functions."""
        print("🔍 Discovering source functions...")
        self.discovered_sources = discover_source_functions()

        if not self.discovered_sources:
            print("❌ No source functions were discovered")
            return False

        print(f"✓ Found {len(self.discovered_sources)} source functions:")
        for source_name, _ in self.discovered_sources:
            print(f"  - {source_name}")

        return True

    def test_get_candles_basic_functionality(self, source_name: str, source_function: Callable) -> TestResult:
        """
        Test 1: Run get_candles_from_{source} with standard parameters.

        Parameters:
        - currency_pair = 'btc-usd'
        - timeframe = 'm1'
        - candles = 5
        - ecc = True

        Expected:
        - Returns a DataFrame with 5 rows
        - Newest timestamp not older than 2 minutes ago
        - Contains at least 5 older timestamps (can contain more)
        """
        print(f"\n🧪 Testing {source_name} with basic parameters...")

        # Test parameters as specified in the comment
        currency_pair = 'btc-usd'
        timeframe = 'm1'
        candles = 5
        ecc = True

        try:
            # Call the source function
            result = source_function(currency_pair, timeframe, candles, ecc)

            # Validate the response and get sample data
            is_valid, errors, sample_data = validate_dataframe_response(result, source_name, candles)

            if not is_valid:
                error_msg = f"Validation failed for {source_name}:\n" + "\n".join(f"  - {error}" for error in errors)
                print(f"❌ {source_name}: FAILED")
                for error in errors:
                    print(f"    {error}")
                return TestResult("basic_functionality", source_name, False, error_msg, sample_data=sample_data)

            # Additional success logging
            details = {}
            if not result.empty:
                newest_ts = result['timestamp'].max()
                oldest_ts = result['timestamp'].min()
                current_time = datetime.now().timestamp()
                age_minutes = (current_time - newest_ts) / 60
                details = {
                    'candles_returned': len(result),
                    'data_age_minutes': round(age_minutes, 1),
                    'time_range_minutes': round((newest_ts - oldest_ts) / 60, 1)
                }
                print(f"✓ {source_name}: Successfully returned {len(result)} candles")
                print(f"  - Data age: {age_minutes:.1f} minutes")
                print(f"  - Time range: {(newest_ts - oldest_ts) / 60:.1f} minutes")

            return TestResult("basic_functionality", source_name, True, "All validations passed", details, sample_data)

        except Exception as e:
            error_msg = f"Exception in {source_name}: {str(e)}"
            print(f"❌ {source_name}: EXCEPTION - {str(e)}")
            # Still try to get sample data even on exception
            sample_data = get_dataframe_sample(None)
            return TestResult("basic_functionality", source_name, False, error_msg, sample_data=sample_data)

    def run_all_tests(self) -> bool:
        """Run all tests on all discovered sources."""
        if not self.discover_sources():
            return False

        print(f"\n🚀 Running tests on {len(self.discovered_sources)} sources...")
        print("=" * 60)

        all_passed = True

        for source_name, source_function in self.discovered_sources:
            # Test 1: Basic functionality
            result = self.test_get_candles_basic_functionality(source_name, source_function)
            self.results.append(result)

            if not result.passed:
                all_passed = False

        return all_passed

    def print_summary(self):
        """Print a summary of all test results."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)

        passed_count = sum(1 for r in self.results if r.passed)
        total_count = len(self.results)

        print(f"Total tests: {total_count}")
        print(f"Passed: {passed_count}")
        print(f"Failed: {total_count - passed_count}")

        if passed_count == total_count:
            print("🎉 ALL TESTS PASSED!")
        else:
            print("❌ Some tests failed")

        print("\nDetailed Results:")
        print("-" * 80)

        for result in self.results:
            status = "✓ PASS" if result.passed else "❌ FAIL"
            print(f"\n{status} | {result.source_name.upper()} | {result.test_name}")

            # Print test details
            if result.details:
                print("  📈 Test Metrics:")
                for key, value in result.details.items():
                    print(f"      {key}: {value}")

            # Print sample data
            if result.sample_data:
                sample = result.sample_data
                if 'total_rows' in sample:
                    print(f"  📊 Data Sample (Total rows: {sample['total_rows']}):")
                    if 'columns' in sample:
                        print(f"      Columns: {sample['columns']}")

                if 'head' in sample and sample['head'] != "No data":
                    print("      📋 First 3 rows:")
                    for line in sample['head'].split('\n'):
                        if line.strip():
                            print(f"        {line}")

                if 'tail' in sample and sample['tail'] != "No data" and sample.get('total_rows', 0) > 3:
                    print("      📋 Last 3 rows:")
                    for line in sample['tail'].split('\n'):
                        if line.strip():
                            print(f"        {line}")

            # Print error details
            if not result.passed:
                print("  ❌ Error Details:")
                for line in result.message.split('\n'):
                    if line.strip():
                        print(f"      {line}")

        print("\n" + "-" * 80)


def run_tests():
    """Main function to run all tests."""
    print("🧪 Source Function Test Suite")
    print("=" * 60)
    print("Testing all get_candles_from_{source} functions")
    print("=" * 60)

    # Create and run test runner
    runner = SourceTestRunner()
    all_passed = runner.run_all_tests()

    # Print summary
    runner.print_summary()

    # Return exit code
    return 0 if all_passed else 1


if __name__ == "__main__":
    # Run the test suite
    exit_code = run_tests()
    sys.exit(exit_code)